import requests
import json

# 测试新的按审查细则组织的输出格式
url = "http://localhost:8000/analyze"

# 准备文件
files = {
    'pdf_file': open('../docs/24年部分可研报告样本和评审结果/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf', 'rb')
}

try:
    print("发送分析请求...")
    response = requests.post(url, files=files, timeout=180)
    
    if response.status_code == 200:
        result = response.json()
        
        print("=== 新的按审查细则组织的输出格式 ===")
        
        # 检查是否有 criteria_analysis 字段
        if 'criteria_analysis' in result:
            criteria_analysis = result['criteria_analysis']
            print(f"共有 {len(criteria_analysis)} 个审查细则")
            
            # 显示前3个审查细则的详细信息
            for i, criterion in enumerate(criteria_analysis[:3]):
                print(f"\n--- 审查细则 {i+1} ---")
                print(f"细则ID: {criterion.get('criterion_id', 'N/A')}")
                print(f"细则内容: {criterion.get('criterion_content', 'N/A')[:100]}...")
                print(f"总体结果: {criterion.get('overall_result', 'N/A')}")
                
                # 显示符合的章节
                compliance_sections = criterion.get('compliance_sections', [])
                if compliance_sections:
                    print(f"符合的章节 ({len(compliance_sections)}个):")
                    for section in compliance_sections[:2]:  # 只显示前2个
                        print(f"  - {section.get('section', 'N/A')}: {section.get('explanation', 'N/A')[:50]}...")
                
                # 显示不符合的章节
                non_compliance_sections = criterion.get('non_compliance_sections', [])
                if non_compliance_sections:
                    print(f"不符合的章节 ({len(non_compliance_sections)}个):")
                    for section in non_compliance_sections[:2]:  # 只显示前2个
                        print(f"  - {section.get('section', 'N/A')}: {section.get('explanation', 'N/A')[:50]}...")
                
                # 显示改进建议
                suggestions = criterion.get('improvement_suggestions', [])
                if suggestions:
                    print(f"改进建议 ({len(suggestions)}条):")
                    for suggestion in suggestions[:2]:  # 只显示前2条
                        print(f"  - {suggestion[:100]}...")
        
        # 显示统计信息
        if 'statistics' in result:
            stats = result['statistics']
            print(f"\n=== 统计信息 ===")
            print(f"总审查细则数: {stats.get('total_criteria', 'N/A')}")
            print(f"总章节数: {stats.get('total_sections', 'N/A')}")
            print(f"合规率: {stats.get('compliance_rate', 'N/A')}%")
            print(f"结果分布: {stats.get('result_distribution', {})}")
        
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print(f"错误信息: {response.text}")
        
except Exception as e:
    print(f"请求异常: {e}")
finally:
    files['pdf_file'].close()
