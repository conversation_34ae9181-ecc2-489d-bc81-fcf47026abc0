<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可研报告评审助手</title>
    <!-- 替换为本地CSS路径 -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/all.min.css" rel="stylesheet">
    <style>
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .result-table {
            margin-top: 20px;
        }
        .criterion-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .criterion-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .criterion-content {
            padding: 15px;
        }
        .result-badge {
            font-size: 0.9em;
            padding: 5px 10px;
        }
        .result-符合 { background-color: #d4edda; color: #155724; }
        .result-基本符合 { background-color: #fff3cd; color: #856404; }
        .result-不符合 { background-color: #f8d7da; color: #721c24; }
        .result-不适用 { background-color: #e2e3e5; color: #383d41; }
        .section-detail {
            font-size: 0.9em;
            margin-top: 10px;
        }
        .statistics-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">可研报告评审助手</h1>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form id="uploadForm">
                            <div class="mb-3">
                                <label for="pdfFile" class="form-label">选择可研报告PDF文件</label>
                                <input type="file" class="form-control" id="pdfFile" accept=".pdf" required>
                            </div>
                            <button type="submit" class="btn btn-primary">开始评审</button>
                        </form>
                    </div>
                </div>

                <div class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在分析报告，请稍候...</p>
                </div>

                <div id="resultSection" class="result-table" style="display: none;">
                    <h3>评审结果</h3>

                    <!-- 统计信息 -->
                    <div id="statisticsCard" class="statistics-card">
                        <h5>评审统计</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>总审查细则</h6>
                                    <span id="totalCriteria" class="badge bg-primary fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>符合</h6>
                                    <span id="compliantCount" class="badge bg-success fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>不符合</h6>
                                    <span id="nonCompliantCount" class="badge bg-danger fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>合规率</h6>
                                    <span id="complianceRate" class="badge bg-info fs-6">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 审查细则列表 -->
                    <div id="criteriaList">
                    </div>

                    <div class="mt-4">
                        <h4>总体评审意见</h4>
                        <div id="summary" class="p-3 bg-light rounded"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const fileInput = document.getElementById('pdfFile');
            const file = fileInput.files[0];
            if (!file) {
                alert('请选择PDF文件');
                return;
            }

            const formData = new FormData();
            formData.append('pdf_file', file);

            // 显示加载动画
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';

            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('评审失败');
                }

                const result = await response.json();

                // 显示统计信息
                if (result.statistics) {
                    const stats = result.statistics;
                    document.getElementById('totalCriteria').textContent = result.criteria_analysis ? result.criteria_analysis.length : 0;
                    document.getElementById('compliantCount').textContent = (stats.result_distribution['符合'] || 0) + (stats.result_distribution['基本符合'] || 0);
                    document.getElementById('nonCompliantCount').textContent = stats.result_distribution['不符合'] || 0;
                    document.getElementById('complianceRate').textContent = stats.compliance_rate + '%';
                }

                // 显示审查细则结果
                const criteriaList = document.getElementById('criteriaList');
                criteriaList.innerHTML = '';

                if (result.criteria_analysis) {
                    console.log(`显示 ${result.criteria_analysis.length} 个审查细则`);
                    result.criteria_analysis.forEach((criterion, index) => {
                        console.log(`处理审查细则 ${index + 1}: ${criterion.criterion_id}`);
                        const criterionCard = document.createElement('div');
                        criterionCard.className = 'criterion-card';

                        // 综合分析内容
                        let comprehensiveAnalysis = '';
                        if (criterion.comprehensive_analysis) {
                            comprehensiveAnalysis = `
                                <div class="alert alert-info mb-3">
                                    <h6><i class="fas fa-chart-line"></i> 全文综合分析：</h6>
                                    <p class="mb-0">${criterion.comprehensive_analysis}</p>
                                </div>
                            `;
                        }

                        // 关键发现
                        let keyFindings = '';
                        if (criterion.key_findings && criterion.key_findings.length > 0) {
                            keyFindings = `
                                <div class="mb-3">
                                    <strong><i class="fas fa-search"></i> 关键发现：</strong>
                                    <ul class="list-unstyled mt-2">
                                        ${criterion.key_findings.map(finding => `
                                            <li class="mb-1"><i class="fas fa-dot-circle text-primary"></i> ${finding}</li>
                                        `).join('')}
                                    </ul>
                                </div>
                            `;
                        }

                        // 生成改进建议 - 优先使用新的recommendations
                        let suggestions = '';
                        const recommendations = criterion.recommendations || criterion.improvement_suggestions || [];
                        if (recommendations.length > 0) {
                            suggestions = `
                                <div class="mb-3">
                                    <strong><i class="fas fa-lightbulb"></i> 改进建议：</strong>
                                    <ul class="list-unstyled mt-2">
                                        ${recommendations.map(suggestion => `
                                            <li class="mb-1"><i class="fas fa-arrow-right text-success"></i> ${suggestion}</li>
                                        `).join('')}
                                    </ul>
                                </div>
                            `;
                        }

                        // 生成章节详情（折叠显示）
                        let sectionDetails = '';
                        if (criterion.section_results && criterion.section_results.length > 0) {
                            const relevantSections = criterion.section_results.filter(s => s.result !== '不适用');
                            if (relevantSections.length > 0) {
                                const collapseId = `collapse-${criterion.criterion_id.replace(/\./g, '-')}`;
                                sectionDetails = `
                                    <div class="mt-3">
                                        <p>
                                            <a class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" href="#${collapseId}" role="button" aria-expanded="false">
                                                <i class="fas fa-list"></i> 查看各章节详细评审情况 (${relevantSections.length}个相关章节)
                                            </a>
                                        </p>
                                        <div class="collapse" id="${collapseId}">
                                            <div class="card card-body">
                                                <ul class="list-unstyled">
                                                    ${relevantSections.map(section => `
                                                        <li class="mb-2">
                                                            <span class="badge result-badge result-${section.result}">${section.result}</span>
                                                            <strong>${section.section}</strong>
                                                            ${section.explanation && section.explanation !== 'N/A' ? `<br><small class="text-muted">${section.explanation}</small>` : ''}
                                                        </li>
                                                    `).join('')}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }
                        }

                        // 确定显示的评审结果 - 优先使用综合分析结果
                        const displayResult = criterion.overall_assessment || criterion.overall_result || '未知';

                        criterionCard.innerHTML = `
                            <div class="criterion-header">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">审查细则 ${criterion.criterion_id}</h6>
                                        <p class="mb-0 text-muted">${criterion.criterion_content}</p>
                                    </div>
                                    <span class="badge result-badge result-${displayResult}">${displayResult}</span>
                                </div>
                            </div>
                            <div class="criterion-content">
                                ${comprehensiveAnalysis}
                                ${keyFindings}
                                ${suggestions}
                                ${sectionDetails}
                            </div>
                        `;

                        criteriaList.appendChild(criterionCard);
                    });
                }

                // 显示总体评审意见
                let summaryContent = '';
                if (result.summary) {
                    if (typeof result.summary === 'string') {
                        summaryContent = result.summary.replace(/\n/g, '<br>');
                    } else if (result.summary.summary_text) {
                        summaryContent = result.summary.summary_text.replace(/\n/g, '<br>');
                    } else {
                        summaryContent = JSON.stringify(result.summary, null, 2);
                    }
                }
                document.getElementById('summary').innerHTML = summaryContent;

                document.getElementById('resultSection').style.display = 'block';
            } catch (error) {
                alert('评审过程中发生错误：' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>